import React from 'react';
import { Clock, Target, Zap, CheckCircle } from 'lucide-react';
import type { TypingMetrics, TypingState } from '../../types';
import { formatTime } from '../../utils/calculations';

interface StatsDisplayProps {
  metrics: TypingMetrics;
  state: TypingState;
}

export const StatsDisplay: React.FC<StatsDisplayProps> = ({ metrics, state }) => {
  const getTimeDisplay = () => {
    if (state.mode === 'time') {
      return formatTime(state.timeLeft);
    } else {
      return formatTime(metrics.elapsedTime);
    }
  };

  const getProgressPercentage = () => {
    if (state.mode === 'time') {
      return ((state.duration - state.timeLeft) / state.duration) * 100;
    } else {
      return (state.userInput.length / state.currentText.length) * 100;
    }
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto">
      {/* WPM */}
      <div className="bg-card/30 border border-border/50 rounded-lg p-4 text-center backdrop-blur-sm">
        <div className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
          WPM
        </div>
        <div className="text-2xl font-bold text-foreground">
          {metrics.wpm}
        </div>
      </div>

      {/* Accuracy */}
      <div className="bg-card/30 border border-border/50 rounded-lg p-4 text-center backdrop-blur-sm">
        <div className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
          ACCURACY
        </div>
        <div className="text-2xl font-bold text-foreground">
          {metrics.accuracy}%
        </div>
      </div>

      {/* Time */}
      <div className="bg-card/30 border border-border/50 rounded-lg p-4 text-center backdrop-blur-sm">
        <div className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
          {state.mode === 'time' ? 'TIME LEFT' : 'TIME'}
        </div>
        <div className="text-2xl font-bold text-foreground">
          {getTimeDisplay()}
        </div>
      </div>

      {/* Progress */}
      <div className="bg-card/30 border border-border/50 rounded-lg p-4 text-center backdrop-blur-sm">
        <div className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
          PROGRESS
        </div>
        <div className="text-2xl font-bold text-foreground">
          {Math.round(getProgressPercentage())}%
        </div>
      </div>
    </div>
  );
};