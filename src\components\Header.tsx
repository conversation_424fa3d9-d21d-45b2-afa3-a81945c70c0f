import React from 'react';
import { Settings } from 'lucide-react';

export const Header: React.FC = () => {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-transparent">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo - Minimal */}
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 text-accent">⚡</div>
            <span className="text-lg font-medium text-foreground">TypeFlow</span>
          </div>
          
          {/* Navigation - Subtle */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#" className="text-muted-foreground hover:text-foreground transition-colors text-sm">Practice</a>
            <a href="#" className="text-muted-foreground hover:text-foreground transition-colors text-sm">Leaderboard</a>
            <a href="#" className="text-muted-foreground hover:text-foreground transition-colors text-sm">About</a>
          </nav>
          
          {/* Settings Icon - Minimal */}
          <button className="text-muted-foreground hover:text-foreground transition-colors">
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </div>
    </header>
  );
};