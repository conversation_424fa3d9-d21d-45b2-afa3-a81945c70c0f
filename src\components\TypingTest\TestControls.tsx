import React from 'react';
import { RotateCcw, Timer, Type } from 'lucide-react';
import type { TestSettings } from '../../types';

interface TestControlsProps {
  settings: TestSettings;
  onSettingsChange: (settings: TestSettings) => void;
  onReset: () => void;
  isActive: boolean;
}

export const TestControls: React.FC<TestControlsProps> = ({
  settings,
  onSettingsChange,
  onReset,
  isActive
}) => {
  const timeModes = [15, 30, 60, 120];
  const wordModes = [10, 25, 50, 100];

  const handleModeChange = (mode: 'time' | 'words') => {
    onSettingsChange({
      ...settings,
      mode
    });
  };

  const handleTimeChange = (timeLimit: number) => {
    onSettingsChange({
      ...settings,
      timeLimit
    });
  };

  const handleWordCountChange = (wordCount: number) => {
    onSettingsChange({
      ...settings,
      wordCount
    });
  };

  return (
    <div className="flex justify-center mb-12">
      <div className="flex items-center space-x-1 bg-card/30 border border-border/50 rounded-lg p-1">
        {/* Mode Toggle */}
        <button
          onClick={() => handleModeChange('time')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
            settings.mode === 'time' 
              ? 'bg-accent text-accent-foreground' 
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          time
        </button>
        <button
          onClick={() => handleModeChange('words')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
            settings.mode === 'words' 
              ? 'bg-accent text-accent-foreground' 
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          words
        </button>
        
        {/* Time Options */}
        {settings.mode === 'time' && (
          <div className="flex space-x-1 ml-4">
            {timeModes.map((time) => (
              <button
                key={time}
                onClick={() => handleTimeChange(time)}
                className={`px-3 py-2 rounded-md text-sm transition-all duration-200 ${
                  settings.timeLimit === time 
                    ? 'bg-accent text-accent-foreground' 
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                {time}
              </button>
            ))}
          </div>
        )}

        {/* Word Options */}
        {settings.mode === 'words' && (
          <div className="flex space-x-1 ml-4">
            {wordModes.map((count) => (
              <button
                key={count}
                onClick={() => handleWordCountChange(count)}
                className={`px-3 py-2 rounded-md text-sm transition-all duration-200 ${
                  settings.wordCount === count 
                    ? 'bg-accent text-accent-foreground' 
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                {count}
              </button>
            ))}
          </div>
        )}
        
        {/* Reset Button */}
        <button
          onClick={onReset}
          disabled={isActive}
          className="ml-4 px-4 py-2 rounded-md text-muted-foreground hover:text-foreground text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          title="Reset test (Tab + Enter)"
        >
          <RotateCcw className="w-3 h-3" />
          <span>reset</span>
        </button>
      </div>
    </div>
  );
};