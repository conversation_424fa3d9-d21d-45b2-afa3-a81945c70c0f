import React, { useEffect, useRef } from 'react';
import { getCharacterStatus } from '../../utils/calculations';
import type { TypingState } from '../../types';

interface TextDisplayProps {
  state: TypingState;
}

export const TextDisplay: React.FC<TextDisplayProps> = ({ state }) => {
  const caretRef = useRef<HTMLSpanElement>(null);
  const textRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to keep caret visible
  useEffect(() => {
    if (caretRef.current && textRef.current) {
      const caretRect = caretRef.current.getBoundingClientRect();
      const textRect = textRef.current.getBoundingClientRect();
      
      if (caretRect.bottom > textRect.bottom - 50) {
        caretRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [state.currentIndex]);

  const renderCharacter = (char: string, index: number) => {
    const status = getCharacterStatus(char, index, state.userInput, state.currentIndex);
    const isCurrentChar = index === state.currentIndex;

    let className = "relative";

    if (index < state.userInput.length) {
      // Typed characters - only text color change
      if (state.userInput[index] === char) {
        className += " text-foreground"; // Correct - clean white
      } else {
        className += " text-destructive"; // Incorrect - clean red, no background
      }
    } else {
      // Untyped characters (including current character)
      className += " text-muted-foreground";
    }

    return (
      <span key={index} className={className}>
        {char}
        {isCurrentChar && (
          <span
            ref={caretRef}
            className="absolute -top-0.5 left-0 w-0.5 h-6 animate-pulse"
            style={{ backgroundColor: '#fbbf24' }}
          />
        )}
      </span>
    );
  };

  const renderText = () => {
    const chars = state.currentText.split('');
    const words: React.ReactNode[] = [];
    let currentWord: React.ReactNode[] = [];
    let charIndex = 0;

    chars.forEach((char, idx) => {
      if (char === ' ') {
        if (currentWord.length > 0) {
          words.push(
            <span key={`word-${words.length}`} className="inline-block">
              {currentWord}
            </span>
          );
          currentWord = [];
        }
        
        // Render space character
        words.push(renderCharacter(char, charIndex));
        charIndex++;
      } else {
        currentWord.push(renderCharacter(char, charIndex));
        charIndex++;
      }
    });

    // Add the last word if exists
    if (currentWord.length > 0) {
      words.push(
        <span key={`word-${words.length}`} className="inline-block">
          {currentWord}
        </span>
      );
    }

    return words;
  };

  return (
    <div className="typing-test-area w-full mx-auto">
      <div
        ref={textRef}
        className="typing-text p-8 rounded-lg min-h-[200px] max-h-[300px] overflow-hidden relative
                   transition-all duration-200"
        style={{
          lineHeight: '1.8'
        }}
      >
        <div className="select-none text-lg md:text-xl leading-relaxed" style={{
          fontFamily: 'JetBrains Mono, monospace',
          letterSpacing: '0.05em'
        }}>
          {renderText()}
        </div>
        
        {/* Progress indicator */}
        <div className="absolute bottom-4 left-8 right-8">
          <div className="w-full bg-border/30 h-0.5 rounded-full overflow-hidden">
            <div 
              className="h-full bg-accent transition-all duration-300 ease-out"
              style={{
                width: `${(state.userInput.length / state.currentText.length) * 100}%`
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
