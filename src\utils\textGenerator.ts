// Common English words for typing practice
const commonWords = [
  'the', 'of', 'and', 'a', 'to', 'in', 'is', 'you', 'that', 'it',
  'he', 'was', 'for', 'on', 'are', 'as', 'with', 'his', 'they', 'i',
  'at', 'be', 'this', 'have', 'from', 'or', 'one', 'had', 'by', 'word',
  'but', 'not', 'what', 'all', 'were', 'we', 'when', 'your', 'can', 'said',
  'there', 'each', 'which', 'she', 'do', 'how', 'their', 'if', 'will', 'up',
  'other', 'about', 'out', 'many', 'then', 'them', 'these', 'so', 'some', 'her',
  'would', 'make', 'like', 'into', 'him', 'time', 'has', 'two', 'more', 'very',
  'after', 'words', 'first', 'where', 'much', 'through', 'back', 'years', 'work', 'way',
  'even', 'good', 'new', 'write', 'our', 'used', 'me', 'man', 'day', 'too',
  'any', 'my', 'say', 'little', 'use', 'your', 'come', 'could', 'now', 'long',
  'before', 'must', 'over', 'think', 'also', 'around', 'another', 'came', 'three', 'word'
];

const quotes = [
  "The quick brown fox jumps over the lazy dog.",
  "In the beginning was the Word, and the Word was with God.",
  "To be or not to be, that is the question.",
  "It was the best of times, it was the worst of times.",
  "Call me Ishmael. Some years ago—never mind how long precisely.",
  "All happy families are alike; each unhappy family is unhappy in its own way.",
  "It is a truth universally acknowledged, that a single man in possession of a good fortune, must be in want of a wife.",
  "In a hole in the ground there lived a hobbit.",
  "The past is a foreign country; they do things differently there.",
  "We are all in the gutter, but some of us are looking at the stars."
];

export function generateWords(count: number): string {
  const words: string[] = [];
  for (let i = 0; i < count; i++) {
    const randomIndex = Math.floor(Math.random() * commonWords.length);
    words.push(commonWords[randomIndex]);
  }
  return words.join(' ');
}

export function generateQuote(): string {
  const randomIndex = Math.floor(Math.random() * quotes.length);
  return quotes[randomIndex];
}

export function generateText(mode: 'time' | 'words', count: number): string {
  if (mode === 'words') {
    return generateWords(count);
  } else {
    // For time mode, generate enough words to last the duration
    // Estimate ~300 characters per minute for average typist
    const estimatedWords = Math.max(50, count * 5);
    return generateWords(estimatedWords);
  }
}

export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}