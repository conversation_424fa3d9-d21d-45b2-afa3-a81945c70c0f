import type { TypingMetrics } from '../types';

export function calculateWPM(correctChars: number, timeInSeconds: number): number {
  if (timeInSeconds === 0) return 0;
  // Standard WPM calculation: (characters / 5) / (time in minutes)
  const minutes = timeInSeconds / 60;
  const words = correctChars / 5;
  return Math.round(words / minutes);
}

export function calculateAccuracy(correctChars: number, totalTyped: number): number {
  if (totalTyped === 0) return 100;
  return Math.round((correctChars / totalTyped) * 100);
}

export function calculateMetrics(
  userInput: string,
  originalText: string,
  startTime: Date | null,
  endTime: Date | null = null
): TypingMetrics {
  if (!startTime) {
    return {
      wpm: 0,
      accuracy: 100,
      correctChars: 0,
      incorrectChars: 0,
      totalChars: 0,
      elapsedTime: 0
    };
  }

  const currentTime = endTime || new Date();
  const elapsedTime = (currentTime.getTime() - startTime.getTime()) / 1000;
  
  let correctChars = 0;
  let incorrectChars = 0;
  const totalChars = userInput.length;

  // Compare each character
  for (let i = 0; i < userInput.length; i++) {
    if (i < originalText.length && userInput[i] === originalText[i]) {
      correctChars++;
    } else {
      incorrectChars++;
    }
  }

  const wpm = calculateWPM(correctChars, elapsedTime);
  const accuracy = calculateAccuracy(correctChars, totalChars);

  return {
    wpm,
    accuracy,
    correctChars,
    incorrectChars,
    totalChars,
    elapsedTime
  };
}

export function getCharacterStatus(
  char: string,
  index: number,
  userInput: string,
  currentIndex: number
): 'untyped' | 'correct' | 'incorrect' | 'current' {
  if (index === currentIndex) {
    return 'current';
  }
  
  if (index >= userInput.length) {
    return 'untyped';
  }
  
  return userInput[index] === char ? 'correct' : 'incorrect';
}

export function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  
  if (mins > 0) {
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }
  
  return secs.toString();
}