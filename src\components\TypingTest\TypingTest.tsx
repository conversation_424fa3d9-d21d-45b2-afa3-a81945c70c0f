import React, { useCallback, useEffect, useState } from 'react';
import { TextDisplay } from './TextDisplay';
import { TestControls } from './TestControls';
import { ResultsModal } from './ResultsModal';
import { FocusCue } from './FocusCue';
import { useTypingTest } from '../../hooks/useTypingTest';
import type { TestSettings } from '../../types';

export const TypingTest: React.FC = () => {
  const [settings, setSettings] = useState<TestSettings>({
    mode: 'time',
    timeLimit: 30,
    wordCount: 25,
    includeNumbers: false,
    includePunctuation: false
  });

  const [showResults, setShowResults] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [showFocusCue, setShowFocusCue] = useState(true);

  const {
    state,
    metrics,
    typeCharacter,
    handleBackspace,
    resetTest,
    finishTest
  } = useTypingTest(settings);

  const handleFocus = () => {
    setIsFocused(true);
    setShowFocusCue(false);
  };
  
  const handleBlur = () => {
    setIsFocused(false);
    setShowFocusCue(true);
  };

  // Handle keyboard input
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Allow all browser shortcuts and dev tools
    if (event.ctrlKey || event.metaKey || event.altKey) {
      // Handle reset shortcut (Tab + Ctrl) only when focused
      if (event.key === 'Tab' && isFocused) {
        event.preventDefault();
        handleReset();
        return;
      }

      // Let all other Ctrl/Cmd/Alt shortcuts pass through
      return;
    }

    // Allow function keys, system keys, and navigation
    if (event.key.startsWith('F') ||
        ['Tab', 'Alt', 'Control', 'Meta', 'Shift', 'Enter', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Home', 'End', 'PageUp', 'PageDown'].includes(event.key)) {
      return;
    }

    // Only handle typing when not in a modal
    if (showResults) return;

    // Auto-focus on printable characters
    if (!isFocused && event.key.length === 1) {
      handleFocus();
    }

    // Only handle typing events when focused
    if (!isFocused) return;

    if (event.key === 'Backspace') {
      event.preventDefault();
      handleBackspace();
    } else if (event.key.length === 1) {
      // Handle printable characters
      event.preventDefault();
      typeCharacter(event.key);
    } else if (event.key === 'Escape') {
      event.preventDefault();
      handleReset();
    }
  }, [state.isActive, showResults, handleBackspace, typeCharacter, isFocused, handleFocus, handleReset]);

  // Add keyboard listeners
  useEffect(() => {
    // Only add listener when component is mounted and not in results modal
    if (!showResults) {
      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }
  }, [handleKeyDown, showResults]);

  // Show results when test is completed
  useEffect(() => {
    if (state.isCompleted && state.endTime) {
      setShowResults(true);
    }
  }, [state.isCompleted, state.endTime]);

  const handleReset = () => {
    resetTest(settings);
    setShowResults(false);
  };

  const handleSettingsChange = (newSettings: TestSettings) => {
    setSettings(newSettings);
    resetTest(newSettings);
    setShowResults(false);
  };

  const handleCloseResults = () => {
    setShowResults(false);
  };

  return (
    <div className="typing-test-container container mx-auto px-6">
      {/* Test Controls */}
      <TestControls
        settings={settings}
        onSettingsChange={handleSettingsChange}
        onReset={handleReset}
        isActive={state.isActive}
      />

      {/* Text Display with Focus */}
      <div className="relative">
        <div
          className={`rounded-lg transition-all duration-200 ${
            isFocused ? 'ring-1 ring-yellow-400/20' : ''
          }`}
          onFocus={handleFocus}
          onBlur={handleBlur}
          tabIndex={0}
        >
          <TextDisplay state={state} />
          <FocusCue isVisible={showFocusCue} onFocus={handleFocus} />
        </div>
      </div>

      {/* Instructions */}
      {!state.isActive && !state.isCompleted && (
        <div className="text-center mt-8 text-muted-foreground">
          <p className="text-sm mb-2">Start typing to begin the test</p>
          <div className="flex flex-wrap justify-center gap-4 text-xs opacity-70">
            <span>• Tab + Ctrl: Reset test</span>
            <span>• Escape: Reset</span>
            <span>• Focus and type to start</span>
          </div>
        </div>
      )}

      {/* Results Modal */}
      <ResultsModal
        isOpen={showResults}
        onClose={handleCloseResults}
        metrics={metrics}
        state={state}
        onRestart={handleReset}
      />
    </div>
  );
};