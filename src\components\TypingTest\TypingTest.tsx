import React, { useCallback, useEffect, useState } from 'react';
import { TextDisplay } from './TextDisplay';
import { TestControls } from './TestControls';
import { StatsDisplay } from './StatsDisplay';
import { ResultsModal } from './ResultsModal';
import { FocusCue } from './FocusCue';
import { useTypingTest } from '../../hooks/useTypingTest';
import type { TestSettings } from '../../types';

export const TypingTest: React.FC = () => {
  const [settings, setSettings] = useState<TestSettings>({
    mode: 'time',
    timeLimit: 30,
    wordCount: 25,
    includeNumbers: false,
    includePunctuation: false
  });

  const [showResults, setShowResults] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [showFocusCue, setShowFocusCue] = useState(true);

  const {
    state,
    metrics,
    typeCharacter,
    handleBackspace,
    resetTest,
    finishTest
  } = useTypingTest(settings);

  const handleFocus = () => {
    setIsFocused(true);
    setShowFocusCue(false);
  };
  
  const handleBlur = () => {
    setIsFocused(false);
    setShowFocusCue(true);
  };

  // Handle keyboard input
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Handle focus when any key is pressed
    if (!isFocused) {
      handleFocus();
    }

    // Prevent default browser shortcuts during typing
    if (state.isActive && ['Tab', 'F5', 'F12'].includes(event.key)) {
      event.preventDefault();
    }

    // Handle reset shortcut (Tab + Enter)
    if (event.key === 'Tab' && event.ctrlKey) {
      event.preventDefault();
      handleReset();
      return;
    }

    // Only handle typing when not in a modal
    if (showResults) return;

    if (event.key === 'Backspace') {
      event.preventDefault();
      handleBackspace();
    } else if (event.key.length === 1) {
      // Handle printable characters
      event.preventDefault();
      typeCharacter(event.key);
    } else if (event.key === 'Escape') {
      event.preventDefault();
      handleReset();
    }
  }, [state.isActive, showResults, handleBackspace, typeCharacter, isFocused]);

  // Add keyboard listeners
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // Show results when test is completed
  useEffect(() => {
    if (state.isCompleted && state.endTime) {
      setShowResults(true);
    }
  }, [state.isCompleted, state.endTime]);

  const handleReset = () => {
    resetTest(settings);
    setShowResults(false);
  };

  const handleSettingsChange = (newSettings: TestSettings) => {
    setSettings(newSettings);
    resetTest(newSettings);
    setShowResults(false);
  };

  const handleCloseResults = () => {
    setShowResults(false);
  };

  return (
    <div className="typing-test-container max-w-4xl mx-auto">
      {/* Test Controls */}
      <TestControls
        settings={settings}
        onSettingsChange={handleSettingsChange}
        onReset={handleReset}
        isActive={state.isActive}
      />

      {/* Stats Display */}
      <StatsDisplay metrics={metrics} state={state} />

      {/* Text Display with Focus */}
      <div className="relative">
        <div 
          className={`rounded-lg transition-all duration-200 ${
            isFocused ? 'ring-1 ring-accent/20' : ''
          }`}
          onFocus={handleFocus}
          onBlur={handleBlur}
          tabIndex={0}
        >
          <TextDisplay state={state} />
          <FocusCue isVisible={showFocusCue} onFocus={handleFocus} />
        </div>
      </div>

      {/* Instructions */}
      {!state.isActive && !state.isCompleted && (
        <div className="text-center mt-8 text-muted-foreground">
          <p className="text-lg mb-2">Start typing to begin the test</p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <span>• Tab + Ctrl: Reset test</span>
            <span>• Escape: Reset</span>
            <span>• Focus and type to start</span>
          </div>
        </div>
      )}

      {/* Results Modal */}
      <ResultsModal
        isOpen={showResults}
        onClose={handleCloseResults}
        metrics={metrics}
        state={state}
        onRestart={handleReset}
      />
    </div>
  );
};