import { useCallback, useEffect, useReducer, useRef } from 'react';
import type { TypingState, TypingMetrics, TestSettings } from '../types';
import { generateText } from '../utils/textGenerator';
import { calculateMetrics } from '../utils/calculations';

interface TypingAction {
  type: 'START_TEST' | 'TYPE_CHARACTER' | 'BACKSPACE' | 'FINISH_TEST' | 'RESET_TEST' | 'UPDATE_TIMER' | 'SET_SETTINGS';
  payload?: any;
}

function typingReducer(state: TypingState, action: TypingAction): TypingState {
  switch (action.type) {
    case 'START_TEST':
      return {
        ...state,
        isActive: true,
        startTime: new Date(),
        endTime: null,
        isCompleted: false
      };

    case 'TYPE_CHARACTER':
      const { character } = action.payload;
      const newUserInput = state.userInput + character;
      const newCurrentIndex = Math.min(newUserInput.length, state.currentText.length);
      
      return {
        ...state,
        userInput: newUserInput,
        currentIndex: newCurrentIndex,
        isCompleted: state.mode === 'words' 
          ? newUserInput.length >= state.currentText.length
          : false
      };

    case 'BACKSPACE':
      if (state.userInput.length === 0) return state;
      
      const newInput = state.userInput.slice(0, -1);
      return {
        ...state,
        userInput: newInput,
        currentIndex: Math.min(newInput.length, state.currentText.length)
      };

    case 'UPDATE_TIMER':
      const { timeLeft } = action.payload;
      return {
        ...state,
        timeLeft,
        isCompleted: timeLeft <= 0 && state.mode === 'time'
      };

    case 'FINISH_TEST':
      return {
        ...state,
        isActive: false,
        isCompleted: true,
        endTime: new Date()
      };

    case 'RESET_TEST':
      const { settings } = action.payload;
      return {
        ...state,
        currentText: generateText(settings.mode, settings.mode === 'time' ? settings.timeLimit : settings.wordCount),
        userInput: '',
        currentIndex: 0,
        startTime: null,
        endTime: null,
        isActive: false,
        isCompleted: false,
        mode: settings.mode,
        duration: settings.timeLimit,
        wordCount: settings.wordCount,
        mistakes: [],
        wpmHistory: [],
        timeLeft: settings.mode === 'time' ? settings.timeLimit : 0
      };

    case 'SET_SETTINGS':
      return {
        ...state,
        mode: action.payload.mode,
        duration: action.payload.timeLimit,
        wordCount: action.payload.wordCount
      };

    default:
      return state;
  }
}

export function useTypingTest(initialSettings: TestSettings) {
  const [state, dispatch] = useReducer(typingReducer, {
    currentText: generateText(initialSettings.mode, initialSettings.mode === 'time' ? initialSettings.timeLimit : initialSettings.wordCount),
    userInput: '',
    currentIndex: 0,
    startTime: null,
    endTime: null,
    isActive: false,
    isCompleted: false,
    mode: initialSettings.mode,
    duration: initialSettings.timeLimit,
    wordCount: initialSettings.wordCount,
    mistakes: [],
    wpmHistory: [],
    timeLeft: initialSettings.mode === 'time' ? initialSettings.timeLimit : 0
  });

  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const metrics: TypingMetrics = calculateMetrics(
    state.userInput,
    state.currentText,
    state.startTime,
    state.endTime
  );

  const startTest = useCallback(() => {
    dispatch({ type: 'START_TEST' });
  }, []);

  const typeCharacter = useCallback((character: string) => {
    if (!state.isActive && !state.isCompleted) {
      dispatch({ type: 'START_TEST' });
    }
    
    if (!state.isCompleted) {
      dispatch({ type: 'TYPE_CHARACTER', payload: { character } });
    }
  }, [state.isActive, state.isCompleted]);

  const handleBackspace = useCallback(() => {
    if (!state.isCompleted) {
      dispatch({ type: 'BACKSPACE' });
    }
  }, [state.isCompleted]);

  const resetTest = useCallback((settings: TestSettings) => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    dispatch({ type: 'RESET_TEST', payload: { settings } });
  }, []);

  const finishTest = useCallback(() => {
    dispatch({ type: 'FINISH_TEST' });
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // Timer effect for time-based tests
  useEffect(() => {
    if (state.isActive && state.mode === 'time' && !state.isCompleted) {
      timerRef.current = setInterval(() => {
        const elapsed = state.startTime 
          ? (new Date().getTime() - state.startTime.getTime()) / 1000
          : 0;
        const remaining = Math.max(0, state.duration - elapsed);
        
        dispatch({ type: 'UPDATE_TIMER', payload: { timeLeft: remaining } });
        
        if (remaining <= 0) {
          dispatch({ type: 'FINISH_TEST' });
        }
      }, 100);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [state.isActive, state.mode, state.duration, state.startTime, state.isCompleted]);

  // Auto-finish word-based tests
  useEffect(() => {
    if (state.mode === 'words' && state.isCompleted && state.isActive) {
      finishTest();
    }
  }, [state.mode, state.isCompleted, state.isActive, finishTest]);

  return {
    state,
    metrics,
    startTest,
    typeCharacter,
    handleBackspace,
    resetTest,
    finishTest
  };
}