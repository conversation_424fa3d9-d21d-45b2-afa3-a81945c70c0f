import React from 'react';
import { MousePointer } from 'lucide-react';

interface FocusCueProps {
  isVisible: boolean;
  onFocus: () => void;
}

export const FocusCue: React.FC<FocusCueProps> = ({ isVisible, onFocus }) => {
  if (!isVisible) return null;
  
  return (
    <div 
      className="absolute inset-0 flex items-center justify-center bg-background/50 backdrop-blur-sm cursor-pointer rounded-lg"
      onClick={onFocus}
    >
      <div className="flex items-center space-x-2 text-muted-foreground">
        <MousePointer className="w-4 h-4" />
        <span className="text-sm">Click here or press any key to focus</span>
      </div>
    </div>
  );
};