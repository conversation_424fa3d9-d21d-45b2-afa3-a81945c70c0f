import React from 'react';
import { X, Trophy, Target, Clock, Zap, Share2, RotateCcw } from 'lucide-react';
import type { TypingMetrics, TypingState } from '../../types';
import { formatTime } from '../../utils/calculations';

interface ResultsModalProps {
  isOpen: boolean;
  onClose: () => void;
  metrics: TypingMetrics;
  state: TypingState;
  onRestart: () => void;
}

export const ResultsModal: React.FC<ResultsModalProps> = ({
  isOpen,
  onClose,
  metrics,
  state,
  onRestart
}) => {
  if (!isOpen) return null;

  const getPerformanceLevel = (wpm: number, accuracy: number) => {
    if (wpm >= 60 && accuracy >= 95) return { level: 'Expert', color: 'text-primary' };
    if (wpm >= 40 && accuracy >= 90) return { level: 'Advanced', color: 'text-typing-correct' };
    if (wpm >= 25 && accuracy >= 85) return { level: 'Intermediate', color: 'text-blue-400' };
    return { level: 'Beginner', color: 'text-secondary-foreground' };
  };

  const performance = getPerformanceLevel(metrics.wpm, metrics.accuracy);

  const handleShare = () => {
    const text = `Just completed a typing test on TypeFlow!\n🏆 ${metrics.wpm} WPM\n🎯 ${metrics.accuracy}% accuracy\n⏱️ ${formatTime(metrics.elapsedTime)}\n\nTry it yourself at TypeFlow!`;
    
    if (navigator.share) {
      navigator.share({
        title: 'TypeFlow Results',
        text,
      });
    } else {
      navigator.clipboard.writeText(text);
      // Could add a toast notification here
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-card border border-border rounded-3xl p-8 max-w-md w-full 
                      shadow-2xl transform animate-scale-in">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Trophy className="w-6 h-6 text-primary" />
            <h2 className="text-2xl font-bold">Test Complete!</h2>
          </div>
          <button
            onClick={onClose}
            className="button-ghost p-2"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Performance Level */}
        <div className="text-center mb-6">
          <div className={`text-lg font-semibold ${performance.color} mb-2`}>
            {performance.level} Level
          </div>
          <div className="text-sm text-muted-foreground">
            {state.mode === 'time' 
              ? `${state.duration}s test` 
              : `${state.wordCount} words test`}
          </div>
        </div>

        {/* Main Stats */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="stats-card text-center">
            <Zap className="w-6 h-6 text-primary mx-auto mb-2" />
            <div className="text-2xl font-bold gradient-text mb-1">
              {metrics.wpm}
            </div>
            <div className="text-sm text-muted-foreground">WPM</div>
          </div>

          <div className="stats-card text-center">
            <Target className="w-6 h-6 text-typing-correct mx-auto mb-2" />
            <div className={`text-2xl font-bold mb-1 ${
              metrics.accuracy >= 95 ? 'text-typing-correct' :
              metrics.accuracy >= 85 ? 'text-primary' :
              'text-typing-incorrect'
            }`}>
              {metrics.accuracy}%
            </div>
            <div className="text-sm text-muted-foreground">Accuracy</div>
          </div>
        </div>

        {/* Detailed Stats */}
        <div className="space-y-3 mb-6">
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Time</span>
            <span className="font-medium">{formatTime(metrics.elapsedTime)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Characters</span>
            <span className="font-medium">{metrics.totalChars}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Correct</span>
            <span className="font-medium text-typing-correct">{metrics.correctChars}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Incorrect</span>
            <span className="font-medium text-typing-incorrect">{metrics.incorrectChars}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            onClick={onRestart}
            className="button-primary flex-1 flex items-center justify-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            Try Again
          </button>
          <button
            onClick={handleShare}
            className="button-secondary flex items-center justify-center gap-2 px-4"
          >
            <Share2 className="w-4 h-4" />
            Share
          </button>
        </div>
      </div>
    </div>
  );
};