@tailwind base;
@tailwind components;
@tailwind utilities;

/* TypeFlow Design System - Optimized for typing tests
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Core Dark Theme */
    --background: 210 6% 10%;
    --foreground: 0 0% 95%;

    /* Card Components */
    --card: 210 8% 14%;
    --card-foreground: 0 0% 95%;

    /* Popover Components */
    --popover: 210 8% 14%;
    --popover-foreground: 0 0% 95%;

    /* Primary Accent - Gold */
    --primary: 45 95% 58%;
    --primary-foreground: 210 6% 10%;

    /* Secondary Elements */
    --secondary: 210 8% 18%;
    --secondary-foreground: 0 0% 80%;

    /* Muted Elements */
    --muted: 210 8% 18%;
    --muted-foreground: 0 0% 50%;

    /* Accent Elements */
    --accent: 210 8% 18%;
    --accent-foreground: 0 0% 95%;

    /* Destructive/Error */
    --destructive: 0 75% 60%;
    --destructive-foreground: 0 0% 95%;

    /* Borders and Inputs */
    --border: 210 8% 25%;
    --input: 210 8% 18%;
    --ring: 45 95% 58%;

    /* Typing-specific colors */
    --typing-correct: 140 60% 60%;
    --typing-incorrect: 0 75% 60%;
    --typing-current: 45 95% 58%;
    --typing-untyped: 0 0% 40%;
    --typing-bg: 210 6% 10%;

    /* Effects */
    --gradient-primary: linear-gradient(135deg, hsl(45 95% 58%), hsl(35 95% 65%));
    --gradient-dark: linear-gradient(135deg, hsl(210 8% 14%), hsl(210 8% 18%));
    --shadow-typing: 0 4px 20px hsl(45 95% 58% / 0.1);
    --shadow-card: 0 4px 12px hsl(210 20% 5% / 0.3);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: 'cv01', 'cv02', 'cv03', 'cv04';
    font-family: 'Inter', sans-serif;
  }
}

/* Typing Animation Keyframes */
@keyframes caret-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes typing-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

@keyframes stats-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes correct-flash {
  0% { background-color: transparent; }
  50% { background-color: hsl(var(--typing-correct) / 0.3); }
  100% { background-color: transparent; }
}

/* Custom Utility Classes */
@layer components {
  .typing-text {
    @apply text-lg md:text-xl leading-relaxed tracking-wide;
    font-family: 'JetBrains Mono', monospace;
    font-feature-settings: 'liga' 0;
    line-height: 1.8;
  }

  .typing-char {
    @apply relative transition-all duration-75;
  }

  .typing-char-untyped {
    @apply text-muted-foreground;
  }

  .typing-char-correct {
    @apply text-foreground;
    animation: correct-flash 0.3s ease-out;
  }

  .typing-char-incorrect {
    @apply text-destructive bg-destructive/20 rounded-sm px-0.5 -mx-0.5;
  }

  .typing-char-current {
    @apply text-primary bg-primary/20 rounded-sm;
  }

  .typing-caret {
    @apply absolute top-0 w-0.5 h-full bg-primary rounded-full;
    animation: caret-blink 1s infinite;
  }

  .stats-card {
    @apply bg-card border border-border rounded-2xl p-6;
    box-shadow: var(--shadow-card);
  }

  .button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 
           rounded-xl px-6 py-3 font-medium transition-all duration-200
           shadow-lg hover:shadow-xl transform hover:scale-105;
    background: var(--gradient-primary);
  }

  .button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80
           border border-border rounded-xl px-4 py-2 font-medium 
           transition-all duration-200 hover:border-primary/50;
  }

  .button-ghost {
    @apply text-muted-foreground hover:text-foreground hover:bg-accent
           rounded-lg px-3 py-2 transition-all duration-200;
  }

  .test-mode-active {
    @apply bg-primary text-primary-foreground border-primary shadow-md;
  }

  .performance-metric {
    @apply text-center;
    animation: stats-slide-up 0.5s ease-out;
  }

  .gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}