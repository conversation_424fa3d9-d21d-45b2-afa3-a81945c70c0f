export interface TypingState {
  currentText: string;
  userInput: string;
  currentIndex: number;
  startTime: Date | null;
  endTime: Date | null;
  isActive: boolean;
  isCompleted: boolean;
  mode: 'time' | 'words';
  duration: number; // seconds for time mode
  wordCount: number; // words for word mode
  mistakes: number[];
  wpmHistory: { time: number; wpm: number }[];
  timeLeft: number;
}

export interface TypingMetrics {
  wpm: number;
  accuracy: number;
  correctChars: number;
  incorrectChars: number;
  totalChars: number;
  elapsedTime: number;
}

export interface TestSettings {
  mode: 'time' | 'words';
  timeLimit: number;
  wordCount: number;
  includeNumbers: boolean;
  includePunctuation: boolean;
}

export interface CharacterState {
  char: string;
  status: 'untyped' | 'correct' | 'incorrect' | 'current';
  index: number;
}

export interface TestResult {
  wpm: number;
  accuracy: number;
  time: number;
  correctChars: number;
  incorrectChars: number;
  totalChars: number;
  date: Date;
  mode: 'time' | 'words';
  duration: number;
}